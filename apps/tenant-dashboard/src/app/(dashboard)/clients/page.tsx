/**
 * @file: clients/page.tsx
 * @description: Страница управления клиентами
 * @dependencies: react, next, @pactcrm/ui
 * @created: 2023-12-01
 */

"use client";

import React from 'react';
import { Users, Plus, Search } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { DataTable } from '@/components/ui-wrappers';

export default function ClientsPage() {
  // Пример данных для таблицы клиентов
  const clients = [
    {
      id: '1',
      name: 'Алексей Смирнов',
      email: '<EMAIL>',
      phone: '+7 (999) 123-45-67',
      contracts: 2,
      status: 'active',
    },
    {
      id: '2',
      name: 'Елена Иванова',
      email: '<EMAIL>',
      phone: '+7 (999) 234-56-78',
      contracts: 1,
      status: 'active',
    },
    {
      id: '3',
      name: 'Д<PERSON><PERSON><PERSON><PERSON><PERSON> Петров',
      email: 'd<PERSON><PERSON>@example.com',
      phone: '+7 (999) 345-67-89',
      contracts: 1,
      status: 'active',
    },
    {
      id: '4',
      name: 'Ольга Козлова',
      email: '<EMAIL>',
      phone: '+7 (999) 456-78-90',
      contracts: 0,
      status: 'inactive',
    },
    {
      id: '5',
      name: 'Сергей Николаев',
      email: '<EMAIL>',
      phone: '+7 (999) 567-89-01',
      contracts: 1,
      status: 'active',
    },
  ];

  // Колонки для таблицы клиентов
  const columns = [
    {
      header: 'Имя',
      accessorKey: 'name' as keyof typeof clients[0],
    },
    {
      header: 'Email',
      accessorKey: 'email' as keyof typeof clients[0],
    },
    {
      header: 'Телефон',
      accessorKey: 'phone' as keyof typeof clients[0],
    },
    {
      header: 'Договоры',
      accessorKey: 'contracts' as keyof typeof clients[0],
    },
    {
      header: 'Статус',
      accessorKey: 'status' as keyof typeof clients[0],
      cell: (item: typeof clients[0]) => (
        <span
          className={`inline-flex rounded-full px-2 py-1 text-xs font-medium ${
            item.status === 'active'
              ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
              : 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300'
          }`}
        >
          {item.status === 'active' ? 'Активен' : 'Неактивен'}
        </span>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Клиенты</h1>
        <Button>
          <Plus className="mr-2 h-4 w-4" />
          Добавить клиента
        </Button>
      </div>

      <div className="flex items-center space-x-2">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Поиск клиентов..."
            className="pl-8"
          />
        </div>
        <Button variant="outline">Фильтры</Button>
      </div>

      <DataTable
        columns={columns}
        data={clients}
        onRowClick={(item) => console.log('Нажата строка:', item)}
      />
    </div>
  );
}
