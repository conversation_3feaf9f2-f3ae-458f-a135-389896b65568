/**
 * @file: properties/page.tsx
 * @description: Страница управления объектами недвижимости
 * @dependencies: react, next, @pactcrm/ui
 * @created: 2023-12-01
 */

"use client";

import React from 'react';
import { Building, Plus, Search } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { DataTable } from '@/components/ui-wrappers';

export default function PropertiesPage() {
  // Пример данных для таблицы объектов недвижимости
  const properties = [
    {
      id: '1',
      name: 'Ж<PERSON> Солнечный',
      address: 'г. Москва, ул. Солнечная, д. 10',
      buildings: 5,
      apartments: 250,
      status: 'active',
    },
    {
      id: '2',
      name: '<PERSON><PERSON> Морской',
      address: 'г. Сочи, ул. Приморская, д. 15',
      buildings: 3,
      apartments: 120,
      status: 'active',
    },
    {
      id: '3',
      name: 'ЖК Центральный',
      address: 'г. Москва, ул. Центральная, д. 5',
      buildings: 2,
      apartments: 80,
      status: 'active',
    },
    {
      id: '4',
      name: 'ЖК Парковый',
      address: 'г. Санкт-Петербург, ул. Парковая, д. 8',
      buildings: 4,
      apartments: 160,
      status: 'active',
    },
    {
      id: '5',
      name: 'ЖК Речной',
      address: 'г. Нижний Новгород, ул. Речная, д. 12',
      buildings: 3,
      apartments: 90,
      status: 'construction',
    },
  ];

  // Колонки для таблицы объектов недвижимости
  const columns = [
    {
      header: 'Название',
      accessorKey: 'name' as keyof typeof properties[0],
    },
    {
      header: 'Адрес',
      accessorKey: 'address' as keyof typeof properties[0],
    },
    {
      header: 'Здания',
      accessorKey: 'buildings' as keyof typeof properties[0],
    },
    {
      header: 'Квартиры',
      accessorKey: 'apartments' as keyof typeof properties[0],
    },
    {
      header: 'Статус',
      accessorKey: 'status' as keyof typeof properties[0],
      cell: (item: typeof properties[0]) => (
        <span
          className={`inline-flex rounded-full px-2 py-1 text-xs font-medium ${
            item.status === 'active'
              ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
              : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300'
          }`}
        >
          {item.status === 'active' ? 'Активен' : 'Строительство'}
        </span>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Объекты недвижимости</h1>
        <Button>
          <Plus className="mr-2 h-4 w-4" />
          Добавить объект
        </Button>
      </div>

      <div className="flex items-center space-x-2">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Поиск объектов..."
            className="pl-8"
          />
        </div>
        <Button variant="outline">Фильтры</Button>
      </div>

      <DataTable
        columns={columns}
        data={properties}
        onRowClick={(item) => console.log('Нажата строка:', item)}
      />
    </div>
  );
}
